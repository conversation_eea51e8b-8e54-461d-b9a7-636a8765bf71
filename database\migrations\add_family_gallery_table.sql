-- =====================================================
-- MIGRATION: Add Family Gallery Table
-- =====================================================
-- This migration adds the family_gallery table for photo management
-- with Cloudinary integration and proper RLS policies.
--
-- 🎯 FEATURES ADDED:
-- ✅ family_gallery table with Cloudinary support
-- ✅ RLS policies for security
-- ✅ Indexes for performance
-- ✅ Triggers for automatic timestamp updates
--
-- 📅 Created: 2025-08-29
-- 🔧 Version: 1.0.0
-- =====================================================

-- Create family_gallery table if it doesn't exist
CREATE TABLE IF NOT EXISTS family_gallery (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    image_public_id TEXT NOT NULL, -- Cloudinary public ID for cleanup
    category VARCHAR(50) NOT NULL DEFAULT 'family',
    tags TEXT[], -- Array of tags for categorization
    location VARCHAR(255),
    uploaded_by VARCHAR(255) NOT NULL DEFAULT 'Admin',
    file_size VARCHAR(20), -- e.g., "2.3 MB"
    dimensions VARCHAR(20), -- e.g., "1920x1080"
    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    is_private BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT family_gallery_title_not_empty CHECK (LENGTH(TRIM(title)) > 0),
    CONSTRAINT family_gallery_image_url_not_empty CHECK (LENGTH(TRIM(image_url)) > 0),
    CONSTRAINT family_gallery_public_id_not_empty CHECK (LENGTH(TRIM(image_public_id)) > 0),
    CONSTRAINT family_gallery_category_valid CHECK (category IN ('family', 'store', 'events', 'products', 'customers', 'celebrations'))
);

-- Create indexes for family_gallery
CREATE INDEX IF NOT EXISTS idx_family_gallery_category ON family_gallery(category);
CREATE INDEX IF NOT EXISTS idx_family_gallery_created_at ON family_gallery(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_family_gallery_uploaded_by ON family_gallery(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_family_gallery_is_private ON family_gallery(is_private);
CREATE INDEX IF NOT EXISTS idx_family_gallery_public_id ON family_gallery(image_public_id);

-- Create or replace the update_updated_at_column function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;

-- Add trigger for updated_at
DROP TRIGGER IF EXISTS update_family_gallery_updated_at ON family_gallery;
CREATE TRIGGER update_family_gallery_updated_at
    BEFORE UPDATE ON family_gallery
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on family_gallery
ALTER TABLE family_gallery ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for family_gallery
DROP POLICY IF EXISTS "Enable all operations for application" ON family_gallery;
CREATE POLICY "Enable all operations for application" ON family_gallery FOR ALL USING (true);

-- Verify the customer_balances view exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.views 
        WHERE table_schema = 'public' 
        AND table_name = 'customer_balances'
    ) THEN
        RAISE NOTICE 'WARNING: customer_balances view does not exist. Please run the main schema file.';
    ELSE
        RAISE NOTICE 'SUCCESS: customer_balances view exists.';
    END IF;
END $$;

-- Add some sample gallery photos if the table is empty
INSERT INTO family_gallery (title, description, image_url, image_public_id, category, tags, location, file_size, dimensions)
SELECT * FROM (VALUES
    ('Pag-bukas sa Tindahan', 'Grand opening sa aming Revantad Store kauban ang buong pamilya', 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400', 'sample_store_opening', 'store', ARRAY['opening', 'family', 'milestone'], 'Cebu City', '2.3 MB', '1920x1080'),
    ('Anniversary Celebration', 'Nag-celebrate mi sa first year sa business', 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=400', 'sample_anniversary', 'celebrations', ARRAY['anniversary', 'celebration', 'milestone'], 'Cebu City', '1.8 MB', '1920x1080'),
    ('Community Festival', 'Nag-participate mi sa local community festival', 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400', 'sample_festival', 'events', ARRAY['community', 'festival', 'participation'], 'Barangay San Jose', '3.1 MB', '1920x1080'),
    ('Mga Produkto sa Store', 'Showcase sa mga bag-ong produkto sa aming tindahan', 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400', 'sample_products', 'products', ARRAY['products', 'showcase', 'inventory'], NULL, '1.5 MB', '1920x1080'),
    ('Family Bonding Time', 'Quality time sa pamilya after work sa tindahan', 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=400', 'sample_family', 'family', ARRAY['family', 'bonding', 'quality-time'], NULL, '2.7 MB', '1920x1080'),
    ('Mga Suki sa Tindahan', 'Mga loyal customers nga nag-visit sa store', 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400', 'sample_customers', 'customers', ARRAY['customers', 'loyalty', 'community'], NULL, '2.1 MB', '1920x1080')
) AS sample_data(title, description, image_url, image_public_id, category, tags, location, file_size, dimensions)
WHERE NOT EXISTS (SELECT 1 FROM family_gallery LIMIT 1);

-- Verification queries
SELECT 
    'Family Gallery Table Verification' as check_type,
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_name = 'family_gallery'
AND table_schema = 'public';

-- Check RLS status
SELECT 
    'RLS Status Verification' as check_type,
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE tablename = 'family_gallery'
AND schemaname = 'public';

-- Count sample photos
SELECT 
    'Sample Photos Count' as check_type,
    COUNT(*) as photo_count
FROM family_gallery;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ Migration completed successfully: Family Gallery table created';
    RAISE NOTICE '🖼️ Gallery functionality is now enabled with sample photos';
    RAISE NOTICE '🔧 RLS policies and indexes have been applied';
END $$;
