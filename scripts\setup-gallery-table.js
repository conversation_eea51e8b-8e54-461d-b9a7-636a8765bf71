#!/usr/bin/env node

/**
 * Setup Gallery Table Script
 * Creates the family_gallery table in Supabase database
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration')
  console.error('Please check your .env.local file for:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function setupGalleryTable() {
  console.log('🚀 Setting up family_gallery table...')

  try {
    // First, check if table already exists
    const { data: existingTable, error: checkError } = await supabase
      .from('family_gallery')
      .select('id')
      .limit(1)

    if (!checkError) {
      console.log('✅ family_gallery table already exists!')

      // Check if it has data
      const { data: photos, error: countError } = await supabase
        .from('family_gallery')
        .select('id')

      if (!countError) {
        console.log(`📊 Table has ${photos?.length || 0} photos`)
        return true
      }
    }

    console.log('📝 Table does not exist, please create it manually in Supabase SQL Editor')
    console.log('📋 Copy and paste this SQL into your Supabase SQL Editor:')
    console.log(`
-- Create family_gallery table
CREATE TABLE IF NOT EXISTS family_gallery (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    image_public_id TEXT NOT NULL,
    category VARCHAR(50) NOT NULL DEFAULT 'family',
    tags TEXT[],
    location VARCHAR(255),
    uploaded_by VARCHAR(255) NOT NULL DEFAULT 'Admin',
    file_size VARCHAR(20),
    dimensions VARCHAR(20),
    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    is_private BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_family_gallery_category ON family_gallery(category);
CREATE INDEX IF NOT EXISTS idx_family_gallery_created_at ON family_gallery(created_at DESC);

-- Enable RLS
ALTER TABLE family_gallery ENABLE ROW LEVEL SECURITY;

-- Create RLS policy
DROP POLICY IF EXISTS "Enable all operations for application" ON family_gallery;
CREATE POLICY "Enable all operations for application" ON family_gallery FOR ALL USING (true);
    `)

    console.log('🌐 Go to: https://supabase.com/dashboard/project/[your-project]/sql')
    return false

    // Add sample data
    const samplePhotos = [
      {
        title: 'Pag-bukas sa Tindahan',
        description: 'Grand opening sa aming Revantad Store kauban ang buong pamilya',
        image_url: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400',
        image_public_id: 'sample_store_opening',
        category: 'store',
        tags: ['opening', 'family', 'milestone'],
        location: 'Cebu City',
        file_size: '2.3 MB',
        dimensions: '1920x1080'
      },
      {
        title: 'Anniversary Celebration',
        description: 'Nag-celebrate mi sa first year sa business',
        image_url: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=400',
        image_public_id: 'sample_anniversary',
        category: 'celebrations',
        tags: ['anniversary', 'celebration', 'milestone'],
        location: 'Cebu City',
        file_size: '1.8 MB',
        dimensions: '1920x1080'
      },
      {
        title: 'Community Festival',
        description: 'Nag-participate mi sa local community festival',
        image_url: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400',
        image_public_id: 'sample_festival',
        category: 'events',
        tags: ['community', 'festival', 'participation'],
        location: 'Barangay San Jose',
        file_size: '3.1 MB',
        dimensions: '1920x1080'
      },
      {
        title: 'Mga Produkto sa Store',
        description: 'Showcase sa mga bag-ong produkto sa aming tindahan',
        image_url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400',
        image_public_id: 'sample_products',
        category: 'products',
        tags: ['products', 'showcase', 'inventory'],
        file_size: '1.5 MB',
        dimensions: '1920x1080'
      },
      {
        title: 'Family Bonding Time',
        description: 'Quality time sa pamilya after work sa tindahan',
        image_url: 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=400',
        image_public_id: 'sample_family',
        category: 'family',
        tags: ['family', 'bonding', 'quality-time'],
        file_size: '2.7 MB',
        dimensions: '1920x1080'
      },
      {
        title: 'Mga Suki sa Tindahan',
        description: 'Mga loyal customers nga nag-visit sa store',
        image_url: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400',
        image_public_id: 'sample_customers',
        category: 'customers',
        tags: ['customers', 'loyalty', 'community'],
        file_size: '2.1 MB',
        dimensions: '1920x1080'
      }
    ]

    const { error: insertError } = await supabase
      .from('family_gallery')
      .insert(samplePhotos)

    if (insertError) {
      console.log('⚠️  Warning: Could not insert sample photos:', insertError.message)
      console.log('✅ Table created successfully, but without sample data')
    } else {
      console.log('✅ Sample photos added successfully!')
    }

    // Verify the setup
    const { data: photos, error: verifyError } = await supabase
      .from('family_gallery')
      .select('count')

    if (verifyError) {
      console.error('❌ Error verifying table:', verifyError)
      return false
    }

    console.log(`✅ Gallery setup complete! Table has ${photos?.length || 0} photos`)
    return true

  } catch (error) {
    console.error('❌ Unexpected error:', error)
    return false
  }
}

// Run the setup
setupGalleryTable()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Gallery table setup completed successfully!')
      console.log('You can now use the Add Photo button in the Family Gallery.')
    } else {
      console.log('\n❌ Gallery table setup failed.')
      console.log('Please check the errors above and try again.')
      process.exit(1)
    }
  })
  .catch((error) => {
    console.error('❌ Setup failed:', error)
    process.exit(1)
  })
