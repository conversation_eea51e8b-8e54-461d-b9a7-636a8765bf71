import { NextRequest, NextResponse } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import cloudinary from '@/lib/cloudinary'
import { logError, logInfo } from '@/lib/logger'
import { supabaseAdmin, withRetry } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch single gallery photo
export const GET = withError<PERSON><PERSON><PERSON>(async (
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  
  const result = await withRetry(async () => {
    const { data: photo, error } = await supabaseAdmin
      .from('family_gallery')
      .select('*')
      .eq('id', id)
      .single()
    
    if (error) throw error
    return photo
  })

  if (!result) {
    return NextResponse.json({ error: 'Photo not found' }, { status: 404 })
  }

  return successResponse({ photo: result })
})

// PUT - Update gallery photo metadata
export const PUT = withError<PERSON>and<PERSON>(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  const body = await request.json()
  
  const {
    title,
    description,
    category,
    tags,
    location,
    is_private
  } = body

  // Validate required fields
  if (!title) {
    return NextResponse.json({ error: 'Title is required' }, { status: 400 })
  }

  const updateData: any = {
    title,
    description,
    category,
    location,
    is_private,
    updated_at: new Date().toISOString()
  }

  // Handle tags array
  if (tags) {
    updateData.tags = Array.isArray(tags) ? tags : tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0)
  }

  const result = await withRetry(async () => {
    const { data: photo, error } = await supabaseAdmin
      .from('family_gallery')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return photo
  })

  if (!result) {
    return NextResponse.json({ error: 'Photo not found' }, { status: 404 })
  }

  logInfo(`Gallery photo updated: ${id}`)

  return successResponse({
    photo: result,
    message: 'Photo updated successfully'
  })
})

// DELETE - Remove gallery photo
export const DELETE = withErrorHandler(async (
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params

  // First, get the photo to retrieve the Cloudinary public_id
  const { data: photo, error: fetchError } = await supabaseAdmin
    .from('family_gallery')
    .select('image_public_id, title')
    .eq('id', id)
    .single()

  if (fetchError || !photo) {
    return NextResponse.json({ error: 'Photo not found' }, { status: 404 })
  }

  // Delete from database first
  const { error: deleteError } = await supabaseAdmin
    .from('family_gallery')
    .delete()
    .eq('id', id)

  if (deleteError) {
    logError('Failed to delete photo from database', deleteError)
    return NextResponse.json({ error: 'Failed to delete photo' }, { status: 500 })
  }

  // Delete from Cloudinary
  if (photo.image_public_id) {
    try {
      const result = await cloudinary.uploader.destroy(photo.image_public_id)
      if (result.result === 'ok') {
        logInfo(`Successfully deleted photo from Cloudinary: ${photo.image_public_id}`)
      } else {
        logError(`Failed to delete photo from Cloudinary: ${result.result}`)
      }
    } catch (cloudinaryError) {
      logError('Error deleting photo from Cloudinary', cloudinaryError)
      // Don't fail the request if Cloudinary deletion fails
      // The database record is already deleted
    }
  }

  logInfo(`Gallery photo deleted: ${id} - ${photo.title}`)

  return successResponse({
    message: 'Photo deleted successfully from gallery'
  })
})

// PATCH - Update photo stats (likes, views)
export const PATCH = withErrorHandler(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) => {
  const { id } = await params
  const body = await request.json()
  
  const { action } = body

  let updateData: any = {}

  switch (action) {
    case 'increment_views':
      // Increment views count
      const { data: currentPhoto } = await supabaseAdmin
        .from('family_gallery')
        .select('views_count')
        .eq('id', id)
        .single()

      if (currentPhoto) {
        updateData.views_count = (currentPhoto.views_count || 0) + 1
      }
      break

    case 'increment_likes':
      // Increment likes count
      const { data: currentPhotoLikes } = await supabaseAdmin
        .from('family_gallery')
        .select('likes_count')
        .eq('id', id)
        .single()

      if (currentPhotoLikes) {
        updateData.likes_count = (currentPhotoLikes.likes_count || 0) + 1
      }
      break

    case 'decrement_likes':
      // Decrement likes count
      const { data: currentPhotoLikesDecr } = await supabaseAdmin
        .from('family_gallery')
        .select('likes_count')
        .eq('id', id)
        .single()

      if (currentPhotoLikesDecr) {
        updateData.likes_count = Math.max((currentPhotoLikesDecr.likes_count || 0) - 1, 0)
      }
      break
      
    default:
      return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
  }

  const result = await withRetry(async () => {
    const { data: photo, error } = await supabaseAdmin
      .from('family_gallery')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return photo
  })

  if (!result) {
    return NextResponse.json({ error: 'Photo not found' }, { status: 404 })
  }

  return successResponse({
    photo: result,
    message: `Photo ${action} updated successfully`
  })
})
