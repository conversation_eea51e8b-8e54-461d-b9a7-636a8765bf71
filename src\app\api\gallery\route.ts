import { NextRequest, NextResponse } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import cloudinary from '@/lib/cloudinary'
import { logError, logInfo } from '@/lib/logger'
import { supabaseAdmin, withRetry } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch all gallery photos with optional filtering
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  
  // Optional filters
  const category = searchParams.get('category')
  const search = searchParams.get('search')
  const isPrivate = searchParams.get('is_private')
  
  // Optional pagination
  const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : 1
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50
  
  let query = supabaseAdmin
    .from('family_gallery')
    .select('*', { count: 'exact' })
    .order('created_at', { ascending: false })
  
  // Apply filters
  if (category) {
    query = query.eq('category', category)
  }
  
  if (search) {
    query = query.or(`title.ilike.%${search}%,description.ilike.%${search}%`)
  }
  
  if (isPrivate !== null) {
    query = query.eq('is_private', isPrivate === 'true')
  }
  
  // Apply pagination
  const offset = (page - 1) * limit
  query = query.range(offset, offset + limit - 1)
  
  const result = await withRetry(async () => {
    const { data: photos, error, count } = await query
    if (error) throw error
    return { photos, count }
  })
  
  const { photos, count } = result
  
  return successResponse({
    photos: photos || [],
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})

// POST - Upload new photo to gallery
export const POST = withErrorHandler(async (request: NextRequest) => {
  try {
    // Check Cloudinary configuration
    if (!process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME || !process.env.CLOUDINARY_API_KEY || !process.env.CLOUDINARY_API_SECRET) {
      logError('Missing Cloudinary configuration')
      return NextResponse.json({ error: 'Server configuration error: Missing Cloudinary credentials' }, { status: 500 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const title = formData.get('title') as string
    const description = formData.get('description') as string
    const category = formData.get('category') as string || 'family'
    const tags = formData.get('tags') as string
    const location = formData.get('location') as string
    const uploadedBy = formData.get('uploaded_by') as string || 'Admin'
    const isPrivate = formData.get('is_private') === 'true'

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    if (!title) {
      return NextResponse.json({ error: 'Title is required' }, { status: 400 })
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'Only image files are allowed' }, { status: 400 })
    }

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File size must be less than 10MB' }, { status: 400 })
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Generate unique public ID
    const timestamp = Date.now()
    const publicId = `family_gallery/${timestamp}_${file.name.replace(/[^a-zA-Z0-9.-]/g, '_')}`

    logInfo(`Uploading gallery photo: ${publicId}`)

    // Upload to Cloudinary
    const uploadResult = await new Promise((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          resource_type: 'image',
          public_id: publicId,
          folder: 'family_gallery',
          transformation: [
            { width: 1200, height: 1200, crop: 'limit' },
            { quality: 'auto:good' },
            { format: 'auto' }
          ],
          timeout: 30000
        },
        (error, result) => {
          if (error) {
            logError('Cloudinary upload error', error)
            reject(new Error(`Cloudinary upload failed: ${error.message}`))
          } else if (result) {
            resolve(result)
          } else {
            reject(new Error('Upload failed: No result returned from Cloudinary'))
          }
        }
      ).end(buffer)
    }) as any

    // Parse tags
    const tagsArray = tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : []

    // Get file dimensions and size
    const fileSizeFormatted = `${(file.size / (1024 * 1024)).toFixed(2)} MB`
    const dimensions = `${uploadResult.width}x${uploadResult.height}`

    // Save to database
    const { data: photo, error: dbError } = await supabaseAdmin
      .from('family_gallery')
      .insert({
        title,
        description,
        image_url: uploadResult.secure_url,
        image_public_id: uploadResult.public_id,
        category,
        tags: tagsArray,
        location,
        uploaded_by: uploadedBy,
        file_size: fileSizeFormatted,
        dimensions,
        is_private: isPrivate
      })
      .select()
      .single()

    if (dbError) {
      // If database save fails, clean up Cloudinary upload
      try {
        await cloudinary.uploader.destroy(uploadResult.public_id)
      } catch (cleanupError) {
        logError('Failed to cleanup Cloudinary upload after database error', cleanupError)
      }
      throw dbError
    }

    logInfo(`Gallery photo uploaded successfully: ${photo.id}`)

    return successResponse({
      photo,
      message: 'Photo uploaded successfully to gallery'
    })

  } catch (error) {
    logError('Gallery upload error', error)
    return NextResponse.json(
      { error: 'Failed to upload photo to gallery' },
      { status: 500 }
    )
  }
})
