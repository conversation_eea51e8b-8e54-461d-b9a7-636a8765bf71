#!/usr/bin/env node

/**
 * Create Gallery Table via API
 * Creates the family_gallery table by making API calls
 */

require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration')
  process.exit(1)
}

async function createTableViaAPI() {
  console.log('🚀 Creating family_gallery table via API...')

  try {
    // Try to create the table using a POST request to a custom endpoint
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/create_family_gallery_table`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({})
    })

    if (response.ok) {
      console.log('✅ Table created successfully via API!')
      return true
    } else {
      console.log('⚠️  API method not available. Using manual approach...')
      
      // Show manual instructions
      console.log('')
      console.log('📝 MANUAL SETUP REQUIRED:')
      console.log('=====================================')
      console.log('1. Go to your Supabase Dashboard')
      console.log('2. Navigate to SQL Editor')
      console.log('3. Copy and paste this SQL:')
      console.log('')
      console.log('-- Create family_gallery table')
      console.log('CREATE TABLE IF NOT EXISTS family_gallery (')
      console.log('    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,')
      console.log('    title VARCHAR(255) NOT NULL,')
      console.log('    description TEXT,')
      console.log('    image_url TEXT NOT NULL,')
      console.log('    image_public_id TEXT NOT NULL,')
      console.log('    category VARCHAR(50) NOT NULL DEFAULT \'family\',')
      console.log('    tags TEXT[],')
      console.log('    location VARCHAR(255),')
      console.log('    uploaded_by VARCHAR(255) NOT NULL DEFAULT \'Admin\',')
      console.log('    file_size VARCHAR(20),')
      console.log('    dimensions VARCHAR(20),')
      console.log('    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),')
      console.log('    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),')
      console.log('    is_private BOOLEAN DEFAULT FALSE,')
      console.log('    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),')
      console.log('    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()')
      console.log(');')
      console.log('')
      console.log('-- Create indexes')
      console.log('CREATE INDEX IF NOT EXISTS idx_family_gallery_category ON family_gallery(category);')
      console.log('CREATE INDEX IF NOT EXISTS idx_family_gallery_created_at ON family_gallery(created_at DESC);')
      console.log('')
      console.log('-- Enable RLS')
      console.log('ALTER TABLE family_gallery ENABLE ROW LEVEL SECURITY;')
      console.log('')
      console.log('-- Create RLS policy')
      console.log('CREATE POLICY "Enable all operations for application" ON family_gallery FOR ALL USING (true);')
      console.log('')
      console.log('-- Add sample data (optional)')
      console.log('INSERT INTO family_gallery (title, description, image_url, image_public_id, category, tags, location, file_size, dimensions)')
      console.log('VALUES ')
      console.log('    (\'Pag-bukas sa Tindahan\', \'Grand opening sa aming Revantad Store\', \'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400\', \'sample_store_opening\', \'store\', ARRAY[\'opening\', \'family\', \'milestone\'], \'Cebu City\', \'2.3 MB\', \'1920x1080\'),')
      console.log('    (\'Family Bonding Time\', \'Quality time sa pamilya after work\', \'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=400\', \'sample_family\', \'family\', ARRAY[\'family\', \'bonding\'], NULL, \'2.7 MB\', \'1920x1080\');')
      console.log('')
      console.log('=====================================')
      console.log('')
      console.log('🌐 Supabase Dashboard URL:')
      console.log(`   ${supabaseUrl.replace('/rest/v1', '')}/project/[project-id]/sql`)
      console.log('')
      return false
    }

  } catch (error) {
    console.error('❌ Error:', error.message)
    return false
  }
}

// Run the creation
createTableViaAPI()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Gallery table created successfully!')
      console.log('✅ The Add Photo button should now work!')
    } else {
      console.log('\n📋 Next Steps:')
      console.log('1. ✅ Mobile Gallery navigation - FIXED')
      console.log('2. ✅ Add Photo button logic - FIXED')
      console.log('3. ❌ Database table - NEEDS MANUAL CREATION')
      console.log('')
      console.log('⚡ After creating the table, refresh the Gallery page and try the Add Photo button!')
    }
  })
  .catch((error) => {
    console.error('❌ Creation failed:', error)
  })
