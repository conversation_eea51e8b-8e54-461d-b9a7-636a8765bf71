#!/usr/bin/env node

/**
 * Direct Gallery Table Creation Script
 * Creates the family_gallery table using direct SQL execution
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function createGalleryTable() {
  console.log('🚀 Creating family_gallery table directly...')

  try {
    // Use the REST API to execute SQL directly
    const response = await fetch(`${supabaseUrl}/rest/v1/rpc/exec_sql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseServiceKey}`,
        'apikey': supabaseServiceKey
      },
      body: JSON.stringify({
        sql: `
          -- Create family_gallery table
          CREATE TABLE IF NOT EXISTS family_gallery (
              id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
              title VARCHAR(255) NOT NULL,
              description TEXT,
              image_url TEXT NOT NULL,
              image_public_id TEXT NOT NULL,
              category VARCHAR(50) NOT NULL DEFAULT 'family',
              tags TEXT[],
              location VARCHAR(255),
              uploaded_by VARCHAR(255) NOT NULL DEFAULT 'Admin',
              file_size VARCHAR(20),
              dimensions VARCHAR(20),
              likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),
              views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
              is_private BOOLEAN DEFAULT FALSE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );

          -- Create indexes
          CREATE INDEX IF NOT EXISTS idx_family_gallery_category ON family_gallery(category);
          CREATE INDEX IF NOT EXISTS idx_family_gallery_created_at ON family_gallery(created_at DESC);

          -- Enable RLS
          ALTER TABLE family_gallery ENABLE ROW LEVEL SECURITY;

          -- Create RLS policy
          DROP POLICY IF EXISTS "Enable all operations for application" ON family_gallery;
          CREATE POLICY "Enable all operations for application" ON family_gallery FOR ALL USING (true);
        `
      })
    })

    if (!response.ok) {
      console.log('⚠️  Direct SQL execution not available. Using alternative method...')
      
      // Try creating table by attempting to insert and catching the error
      const { error } = await supabase
        .from('family_gallery')
        .select('id')
        .limit(1)

      if (error && error.code === '42P01') {
        console.log('📝 Please create the table manually in Supabase SQL Editor:')
        console.log('🌐 Go to: https://supabase.com/dashboard/project/[your-project]/sql')
        console.log('📋 Copy and paste the SQL from create-gallery-table.sql')
        return false
      }
    }

    console.log('✅ Table creation command executed!')

    // Verify the table exists
    const { data, error } = await supabase
      .from('family_gallery')
      .select('id')
      .limit(1)

    if (error) {
      console.log('❌ Table verification failed:', error.message)
      return false
    }

    console.log('✅ family_gallery table verified!')

    // Add sample data
    const samplePhotos = [
      {
        title: 'Pag-bukas sa Tindahan',
        description: 'Grand opening sa aming Revantad Store kauban ang buong pamilya',
        image_url: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400',
        image_public_id: 'sample_store_opening',
        category: 'store',
        tags: ['opening', 'family', 'milestone'],
        location: 'Cebu City',
        file_size: '2.3 MB',
        dimensions: '1920x1080'
      },
      {
        title: 'Anniversary Celebration',
        description: 'Nag-celebrate mi sa first year sa business',
        image_url: 'https://images.unsplash.com/photo-1530103862676-de8c9debad1d?w=400',
        image_public_id: 'sample_anniversary',
        category: 'celebrations',
        tags: ['anniversary', 'celebration', 'milestone'],
        location: 'Cebu City',
        file_size: '1.8 MB',
        dimensions: '1920x1080'
      },
      {
        title: 'Family Bonding Time',
        description: 'Quality time sa pamilya after work sa tindahan',
        image_url: 'https://images.unsplash.com/photo-1511895426328-dc8714191300?w=400',
        image_public_id: 'sample_family',
        category: 'family',
        tags: ['family', 'bonding', 'quality-time'],
        file_size: '2.7 MB',
        dimensions: '1920x1080'
      }
    ]

    const { error: insertError } = await supabase
      .from('family_gallery')
      .insert(samplePhotos)

    if (insertError) {
      console.log('⚠️  Could not insert sample photos:', insertError.message)
    } else {
      console.log('✅ Sample photos added!')
    }

    return true

  } catch (error) {
    console.error('❌ Error:', error.message)
    return false
  }
}

// Run the creation
createGalleryTable()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Gallery table setup completed!')
      console.log('✅ You can now use the Add Photo button in the Family Gallery.')
    } else {
      console.log('\n📝 Manual setup required:')
      console.log('1. Go to your Supabase dashboard')
      console.log('2. Navigate to SQL Editor')
      console.log('3. Run the SQL from create-gallery-table.sql')
    }
  })
  .catch((error) => {
    console.error('❌ Setup failed:', error)
  })
