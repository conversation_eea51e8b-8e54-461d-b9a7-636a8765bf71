'use client'

import {
  Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon,
  Search, Filter, Grid, List, Calendar, Eye, Star, Users, Camera,
  Play, Pause, ChevronLeft, ChevronRight, X,
  FolderOpen, Tag, Clock, Activity,
  Settings, MoreVertical, RefreshCw, SortAsc, SortDesc
} from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useMemo, useRef, useEffect, useCallback } from 'react'

import { API_ENDPOINTS } from '@/constants'
import { logError, logInfo } from '@/lib/logger'
import { useToast } from '@/components'

interface Photo {
  id: string
  title: string
  description: string
  image_url: string
  image_public_id: string
  category: 'family' | 'store' | 'events' | 'products' | 'customers' | 'celebrations'
  tags: string[]
  location?: string
  uploaded_by: string
  file_size: string
  dimensions: string
  likes_count?: number
  views_count?: number
  is_private: boolean
  created_at: string
  updated_at: string
}

export default function FamilyGallery() {
  const { resolvedTheme } = useTheme()
  const { showSuccess, showError } = useToast()
  const [photos, setPhotos] = useState<Photo[]>([])
  const [loading, setLoading] = useState(true)
  const [uploading, setUploading] = useState(false)

  // Fetch photos from API
  const fetchPhotos = useCallback(async () => {
    try {
      setLoading(true)

      let response
      try {
        response = await fetch(`${API_ENDPOINTS.gallery}?limit=100`)
      } catch (fetchError) {
        // Network error or fetch failed
        console.warn('Gallery API not available. Showing empty gallery.')
        setPhotos([])
        return
      }

      if (!response.ok) {
        // If table doesn't exist, show empty gallery instead of error
        if (response.status === 500) {
          console.warn('Gallery table not created yet. Showing empty gallery.')
          setPhotos([])
          return
        }
        console.warn('Failed to fetch photos:', response.status)
        setPhotos([])
        return
      }

      const data = await response.json()
      console.log('Gallery API Response:', data) // Debug log

      if (data.photos && Array.isArray(data.photos)) {
        setPhotos(data.photos)
        logInfo(`Loaded ${data.photos.length} photos from gallery`)
      } else {
        console.warn('Invalid photos data structure:', data)
        setPhotos([])
      }
    } catch (error) {
      // Silently handle any other errors and show empty gallery
      setPhotos([])
      console.warn('Gallery API error. Showing empty gallery.')
    } finally {
      setLoading(false)
    }
  }, [])

  // Load photos on component mount
  useEffect(() => {
    console.log('FamilyGallery component mounted, fetching photos...')
    fetchPhotos()
  }, [fetchPhotos])

  // Debug: Log photos state changes
  useEffect(() => {
    console.log('Photos state updated:', photos?.length || 0, 'photos')
    if (photos && photos.length > 0) {
      console.log('First photo:', photos[0])
    }
  }, [photos])

  // Enhanced state management
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)
  const [isLightboxOpen, setIsLightboxOpen] = useState(false)
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0)
  const [isSlideshow, setIsSlideshow] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [sortBy, setSortBy] = useState<'created_at' | 'likes_count' | 'views_count' | 'title'>('created_at')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedPhotos, setSelectedPhotos] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const [uploadForm, setUploadForm] = useState({
    title: '',
    description: '',
    file: null as File | null,
    category: 'family' as Photo['category'],
    tags: '',
    is_private: false,
    location: ''
  })

  // Upload photo function
  const handleUpload = async () => {
    if (!uploadForm.file || !uploadForm.title) {
      showError('Please provide a file and title')
      return
    }

    try {
      setUploading(true)

      const formData = new FormData()
      formData.append('file', uploadForm.file)
      formData.append('title', uploadForm.title)
      formData.append('description', uploadForm.description)
      formData.append('category', uploadForm.category)
      formData.append('tags', uploadForm.tags)
      formData.append('location', uploadForm.location)
      formData.append('is_private', uploadForm.is_private.toString())
      formData.append('uploaded_by', 'Admin')

      let response
      try {
        response = await fetch(API_ENDPOINTS.gallery, {
          method: 'POST',
          body: formData
        })
      } catch (fetchError) {
        // Network error or fetch failed
        showError('Unable to connect to gallery service. Please check your connection.')
        return
      }

      if (!response.ok) {
        // If table doesn't exist, show helpful message
        if (response.status === 500) {
          showError('Gallery database table not created yet. Please create the family_gallery table first.')
          return
        }
        try {
          const errorData = await response.json()
          showError(errorData.error || 'Failed to upload photo')
        } catch {
          showError('Failed to upload photo')
        }
        return
      }

      const data = await response.json()
      console.log('Upload response data:', data) // Debug log

      // Add new photo to the list
      if (data.photo) {
        setPhotos(prev => [data.photo, ...prev])
        console.log('Photo added to state:', data.photo) // Debug log
      } else {
        console.warn('No photo data in response:', data)
        // Fallback: refresh the entire gallery
        await fetchPhotos()
      }

      // Reset form
      setUploadForm({
        title: '',
        description: '',
        file: null,
        category: 'family',
        tags: '',
        is_private: false,
        location: ''
      })

      setIsUploadModalOpen(false)
      showSuccess('Photo uploaded successfully!')
      logInfo(`Photo uploaded: ${uploadForm.title}`)

      // Force refresh the gallery to ensure new photo appears
      setTimeout(() => {
        fetchPhotos()
      }, 1000)

    } catch (error) {
      console.warn('Upload error:', error)
      showError('Upload failed. Please ensure the gallery database table is created.')
    } finally {
      setUploading(false)
    }
  }

  // Enhanced filtering and sorting logic
  const filteredAndSortedPhotos = useMemo(() => {
    // Handle loading state and ensure photos is always an array
    if (!photos || !Array.isArray(photos)) {
      return []
    }

    // First filter out null/undefined photos
    const validPhotos = photos.filter(photo => photo && typeof photo === 'object')

    const filtered = validPhotos.filter(photo => {
      if (!photo) return false

      const matchesSearch = searchTerm === '' ||
        (photo.title && photo.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (photo.description && photo.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (photo.tags && Array.isArray(photo.tags) && photo.tags.some(tag => tag && tag.toLowerCase().includes(searchTerm.toLowerCase())))

      const matchesCategory = selectedCategory === 'all' || photo.category === selectedCategory

      return matchesSearch && matchesCategory
    })

    filtered.sort((a, b) => {
      // Safety check for null/undefined items
      if (!a || !b) return 0

      let comparison = 0
      switch (sortBy) {
        case 'created_at':
          const dateA = a.created_at ? new Date(a.created_at).getTime() : 0
          const dateB = b.created_at ? new Date(b.created_at).getTime() : 0
          comparison = dateA - dateB
          break
        case 'likes_count':
          comparison = (a.likes_count || 0) - (b.likes_count || 0)
          break
        case 'views_count':
          comparison = (a.views_count || 0) - (b.views_count || 0)
          break
        case 'title':
          const titleA = a.title || ''
          const titleB = b.title || ''
          comparison = titleA.localeCompare(titleB)
          break
      }
      return sortOrder === 'desc' ? -comparison : comparison
    })

    return filtered
  }, [photos, searchTerm, selectedCategory, sortBy, sortOrder])

  // Enhanced photo interactions
  const handleLike = async (photoId: string) => {
    try {
      const response = await fetch(`${API_ENDPOINTS.gallery}/${photoId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'increment_likes' })
      })

      if (response.ok) {
        const data = await response.json()
        setPhotos(photos.map(photo =>
          photo.id === photoId ? data.photo : photo
        ))
      }
    } catch (error) {
      logError('Error updating likes', error)
    }
  }

  const handleView = async (photoId: string) => {
    try {
      const response = await fetch(`${API_ENDPOINTS.gallery}/${photoId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ action: 'increment_views' })
      })

      if (response.ok) {
        const data = await response.json()
        setPhotos(photos.map(photo =>
          photo.id === photoId ? data.photo : photo
        ))
      }
    } catch (error) {
      logError('Error updating views', error)
    }
  }

  const handleDelete = async (photoId: string) => {
    if (confirm('Sigurado ka ba nga i-delete ni nga photo?')) {
      try {
        const response = await fetch(`${API_ENDPOINTS.gallery}/${photoId}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          setPhotos(photos.filter(photo => photo.id !== photoId))
          setSelectedPhotos(selectedPhotos.filter(id => id !== photoId))
          showSuccess('Photo deleted successfully')
        } else {
          showError('Failed to delete photo')
          return
        }
      } catch (error) {
        logError('Error deleting photo', error)
        showError('Failed to delete photo')
      }
    }
  }

  const handleBulkDelete = () => {
    if (selectedPhotos.length > 0 && confirm(`Sigurado ka ba nga i-delete ang ${selectedPhotos.length} ka photos?`)) {
      setPhotos(photos.filter(photo => !selectedPhotos.includes(photo.id)))
      setSelectedPhotos([])
    }
  }

  const handleSelectPhoto = (photoId: string) => {
    setSelectedPhotos(prev =>
      prev.includes(photoId)
        ? prev.filter(id => id !== photoId)
        : [...prev, photoId]
    )
  }

  const handleSelectAll = () => {
    if (selectedPhotos.length === filteredAndSortedPhotos.length) {
      setSelectedPhotos([])
    } else {
      setSelectedPhotos(filteredAndSortedPhotos.map(photo => photo.id))
    }
  }

  // Enhanced lightbox functionality
  const openLightbox = (photoIndex: number) => {
    setCurrentPhotoIndex(photoIndex)
    setIsLightboxOpen(true)
    const photo = filteredAndSortedPhotos[photoIndex]
    if (photo) {
      handleView(photo.id)
    }
  }

  const closeLightbox = () => {
    setIsLightboxOpen(false)
    setIsSlideshow(false)
  }

  const nextPhoto = () => {
    const nextIndex = (currentPhotoIndex + 1) % filteredAndSortedPhotos.length
    setCurrentPhotoIndex(nextIndex)
    const photo = filteredAndSortedPhotos[nextIndex]
    if (photo) {
      handleView(photo.id)
    }
  }

  const prevPhoto = () => {
    const prevIndex = currentPhotoIndex === 0 ? filteredAndSortedPhotos.length - 1 : currentPhotoIndex - 1
    setCurrentPhotoIndex(prevIndex)
    const photo = filteredAndSortedPhotos[prevIndex]
    if (photo) {
      handleView(photo.id)
    }
  }

  const toggleSlideshow = () => {
    setIsSlideshow(!isSlideshow)
  }



  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setUploadForm({ ...uploadForm, file })
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const file = e.dataTransfer.files[0]
    if (file && file.type.startsWith('image/')) {
      setUploadForm({ ...uploadForm, file })
    }
  }

  // Gallery statistics
  const galleryStats = useMemo(() => {
    // Ensure photos is always an array and handle loading state
    if (!photos || !Array.isArray(photos)) {
      return {
        totalPhotos: 0,
        totalViews: 0,
        totalLikes: 0,
        categoryCounts: {},
        averageViews: 0,
        mostPopular: null
      }
    }

    // Filter out any null/undefined photos and ensure they have required properties
    const validPhotos = photos.filter(photo => photo && typeof photo === 'object')

    const totalViews = validPhotos.reduce((sum, photo) => {
      return sum + (photo?.views_count || 0)
    }, 0)

    const totalLikes = validPhotos.reduce((sum, photo) => {
      return sum + (photo?.likes_count || 0)
    }, 0)

    const categoryCounts = validPhotos.reduce((acc, photo) => {
      if (photo?.category) {
        acc[photo.category] = (acc[photo.category] || 0) + 1
      }
      return acc
    }, {} as Record<string, number>)

    return {
      totalPhotos: validPhotos.length,
      totalViews,
      totalLikes,
      categoryCounts,
      averageViews: validPhotos.length > 0 ? Math.round(totalViews / validPhotos.length) : 0,
      mostPopular: validPhotos.length > 0 ? validPhotos.reduce((prev, current) => {
        if (!prev || !current) return prev || current
        const prevViews = prev?.views_count || 0
        const currentViews = current?.views_count || 0
        return prevViews > currentViews ? prev : current
      }, validPhotos[0]) : null
    }
  }, [photos])

  const categories = [
    { id: 'all', label: 'Tanan', icon: Grid },
    { id: 'family', label: 'Pamilya', icon: Users },
    { id: 'store', label: 'Tindahan', icon: FolderOpen },
    { id: 'events', label: 'Mga Event', icon: Calendar },
    { id: 'products', label: 'Produkto', icon: Tag },
    { id: 'customers', label: 'Mga Suki', icon: Heart },
    { id: 'celebrations', label: 'Selebrasyon', icon: Star }
  ]

  return (
    <div className="space-y-6 animate-fade-in">
      {/* Enhanced Header with Professional Design */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-lg shadow-lg">
              <Camera className="h-6 w-6 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                Family Gallery
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Mga memories ug moments sa inyong pamilya ug tindahan
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 mt-4">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-blue-600 dark:text-blue-400">Total Photos</p>
                  <p className="text-lg font-bold text-blue-900 dark:text-blue-300">{galleryStats.totalPhotos}</p>
                </div>
                <ImageIcon className="h-5 w-5 text-blue-500" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-green-600 dark:text-green-400">Total Views</p>
                  <p className="text-lg font-bold text-green-900 dark:text-green-300">{galleryStats.totalViews}</p>
                </div>
                <Eye className="h-5 w-5 text-green-500" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-red-600 dark:text-red-400">Total Likes</p>
                  <p className="text-lg font-bold text-red-900 dark:text-red-300">{galleryStats.totalLikes}</p>
                </div>
                <Heart className="h-5 w-5 text-red-500" />
              </div>
            </div>

            <div className="bg-gradient-to-r from-amber-50 to-yellow-100 dark:from-amber-900/20 dark:to-yellow-800/20 p-3 rounded-lg border border-amber-200 dark:border-amber-800">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-xs font-medium text-amber-700 dark:text-amber-400">Selected</p>
                  <p className="text-lg font-bold text-amber-900 dark:text-amber-300">{selectedPhotos.length}</p>
                </div>
                <Activity className="h-5 w-5 text-amber-600" />
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          {selectedPhotos.length > 0 && (
            <>
              <button
                onClick={handleBulkDelete}
                className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all"
              >
                <Trash2 className="h-4 w-4" />
                Delete Selected ({selectedPhotos.length})
              </button>
              <button
                onClick={() => setSelectedPhotos([])}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all"
              >
                <X className="h-4 w-4" />
                Clear Selection
              </button>
            </>
          )}

          <button
            onClick={fetchPhotos}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors bg-white dark:bg-slate-700 text-gray-900 dark:text-white flex items-center gap-2 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>

          <button
            onClick={() => setIsUploadModalOpen(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 text-sm transition-all shadow-md"
          >
            <Plus className="h-4 w-4" />
            Add Photo
          </button>
        </div>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="card p-6 border-l-4 border-l-amber-600">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
            <Search className="h-5 w-5 text-amber-600" />
            Search & Filters
          </h3>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white flex items-center gap-1 transition-colors"
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide Filters' : 'Show Filters'}
            </button>

            {/* View Mode Toggle */}
            <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-1 text-xs font-medium rounded-md transition-all ${
                  viewMode === 'list'
                    ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>

        {/* Basic Search and Controls */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search photos, descriptions, tags..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 text-sm"
            />
          </div>

          {/* Category Filter */}
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white text-sm"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>{category.label}</option>
            ))}
          </select>

          {/* Sort Options */}
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as typeof sortBy)}
              className="flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent bg-white dark:bg-slate-700 text-gray-900 dark:text-white text-sm"
            >
              <option value="date">Sort by Date</option>
              <option value="likes">Sort by Likes</option>
              <option value="views">Sort by Views</option>
              <option value="title">Sort by Title</option>
            </select>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors bg-white dark:bg-slate-700 text-gray-900 dark:text-white"
              title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
            >
              {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
            </button>
          </div>

          {/* Bulk Actions */}
          <div className="flex gap-2">
            <button
              onClick={handleSelectAll}
              className="flex-1 px-3 py-2.5 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors bg-white dark:bg-slate-700 text-gray-900 dark:text-white text-sm"
            >
              {selectedPhotos.length === filteredAndSortedPhotos.length ? 'Deselect All' : 'Select All'}
            </button>
          </div>
        </div>

        {/* Advanced Filters (Collapsible) */}
        {showFilters && (
          <div className="border-t border-gray-200 dark:border-gray-700 pt-4 animate-fade-in-up">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {categories.slice(1).map(category => {
                const Icon = category.icon
                const count = galleryStats.categoryCounts[category.id] || 0
                return (
                  <div key={category.id} className="text-center">
                    <button
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full p-3 rounded-lg border-2 transition-all ${
                        selectedCategory === category.id
                          ? 'border-amber-600 bg-amber-50 dark:bg-amber-900/20'
                          : 'border-gray-200 dark:border-gray-700 hover:border-amber-300'
                      }`}
                    >
                      <Icon className={`h-6 w-6 mx-auto mb-2 ${
                        selectedCategory === category.id ? 'text-amber-600' : 'text-gray-500'
                      }`} />
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{category.label}</p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">{count} photos</p>
                    </button>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Photo Display */}
      <div className="card overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center gap-2">
              <ImageIcon className="h-5 w-5 text-amber-600" />
              Photo Gallery ({filteredAndSortedPhotos.length})
            </h3>

            {selectedPhotos.length > 0 && (
              <span className="text-xs bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400 px-2 py-1 rounded-full">
                {selectedPhotos.length} selected
              </span>
            )}
          </div>
        </div>

        {viewMode === 'grid' ? (
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredAndSortedPhotos.map((photo, index) => (
                <div key={photo.id} className="group relative">
                  {/* Selection Checkbox */}
                  <div className="absolute top-2 left-2 z-10">
                    <input
                      type="checkbox"
                      checked={selectedPhotos.includes(photo.id)}
                      onChange={() => handleSelectPhoto(photo.id)}
                      className="w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"
                    />
                  </div>

                  {/* Category Badge */}
                  <div className="absolute top-2 right-2 z-10">
                    <span
                      className="px-2 py-1 text-xs font-medium rounded-full"
                      style={{
                        backgroundColor:
                          photo.category === 'family' ? (resolvedTheme === 'dark' ? '#1e3a8a' : '#3b82f6') :
                          photo.category === 'store' ? (resolvedTheme === 'dark' ? '#166534' : '#22c55e') :
                          photo.category === 'events' ? (resolvedTheme === 'dark' ? '#a16207' : '#eab308') :
                          photo.category === 'products' ? (resolvedTheme === 'dark' ? '#d97706' : '#f59e0b') :
                          photo.category === 'customers' ? (resolvedTheme === 'dark' ? '#be185d' : '#ec4899') :
                          (resolvedTheme === 'dark' ? '#ea580c' : '#f97316'),
                        color: resolvedTheme === 'dark' ? '#ffffff' : '#ffffff'
                      }}
                    >
                      {categories.find(c => c.id === photo.category)?.label}
                    </span>
                  </div>

                  <div className="card overflow-hidden hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                    <div className="relative aspect-video bg-gray-200 dark:bg-gray-700 cursor-pointer" onClick={() => openLightbox(index)}>
                      {photo.image_url ? (
                        <img
                          src={photo.image_url}
                          alt={photo.title}
                          className="w-full h-full object-cover"
                          loading="lazy"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 flex items-center justify-center">
                          <ImageIcon className="h-16 w-16 text-gray-400" />
                        </div>
                      )}

                      {/* Hover Overlay */}
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <div className="flex items-center gap-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              openLightbox(index)
                            }}
                            className="bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors"
                          >
                            <Eye className="h-4 w-4 inline mr-1" />
                            View
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              setSelectedPhoto(photo)
                            }}
                            className="bg-white text-gray-900 px-3 py-2 rounded-lg font-medium text-sm hover:bg-gray-100 transition-colors"
                          >
                            <Settings className="h-4 w-4 inline mr-1" />
                            Details
                          </button>
                        </div>
                      </div>


                    </div>

                    <div className="p-4">
                      <h3
                        className="font-semibold mb-1 line-clamp-1"
                        style={{
                          color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                        }}
                      >
                        {photo.title}
                      </h3>
                      <p
                        className="text-sm mb-3 line-clamp-2"
                        style={{
                          color: resolvedTheme === 'dark' ? '#9ca3af' : '#374151'
                        }}
                      >
                        {photo.description}
                      </p>

                      {/* Tags */}
                      {photo.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {photo.tags.slice(0, 3).map(tag => (
                            <span
                              key={tag}
                              className="inline-flex px-2 py-1 text-xs rounded-md"
                              style={{
                                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#e5e7eb',
                                color: resolvedTheme === 'dark' ? '#9ca3af' : '#1f2937'
                              }}
                            >
                              #{tag}
                            </span>
                          ))}
                          {photo.tags.length > 3 && (
                            <span
                              className="text-xs"
                              style={{
                                color: resolvedTheme === 'dark' ? '#9ca3af' : '#4b5563'
                              }}
                            >
                              +{photo.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      )}

                      <div className="flex items-center justify-between">
                        <div
                          className="flex items-center gap-2 text-xs"
                          style={{
                            color: resolvedTheme === 'dark' ? '#9ca3af' : '#4b5563'
                          }}
                        >
                          <Clock className="h-3 w-3" />
                          {new Date(photo.created_at).toLocaleDateString('en-PH')}
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleLike(photo.id)}
                            className="flex items-center space-x-1 text-sm transition-colors"
                            style={{
                              color: resolvedTheme === 'dark' ? '#9ca3af' : '#4b5563'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = '#ef4444'
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = resolvedTheme === 'dark' ? '#9ca3af' : '#4b5563'
                            }}
                          >
                            <Heart className="h-4 w-4" />
                            <span>{photo.likes_count || 0}</span>
                          </button>

                          <button
                            className="transition-colors"
                            style={{
                              color: resolvedTheme === 'dark' ? '#9ca3af' : '#4b5563'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.color = '#3b82f6'
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.color = resolvedTheme === 'dark' ? '#9ca3af' : '#4b5563'
                            }}
                          >
                            <Share2 className="h-4 w-4" />
                          </button>

                          <div className="relative group">
                            <button
                              className="transition-colors"
                              style={{
                                color: resolvedTheme === 'dark' ? '#9ca3af' : '#374151'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.color = resolvedTheme === 'dark' ? '#d1d5db' : '#111827'
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.color = resolvedTheme === 'dark' ? '#9ca3af' : '#374151'
                              }}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </button>


                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          // List View
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {filteredAndSortedPhotos.map((photo, index) => (
              <div key={photo.id} className="p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-all duration-200 group">
                <div className="flex items-start space-x-4">
                  <input
                    type="checkbox"
                    checked={selectedPhotos.includes(photo.id)}
                    onChange={() => handleSelectPhoto(photo.id)}
                    className="mt-1 w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"
                  />

                  <div className="w-20 h-20 bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center cursor-pointer" onClick={() => openLightbox(index)}>
                    <ImageIcon className="h-8 w-8 text-gray-400" />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-1">
                          {photo.title}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {photo.description}
                        </p>

                        <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(photo.date).toLocaleDateString('en-PH')}
                          </span>
                          <span className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {photo.views} views
                          </span>
                          <span className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {photo.likes} likes
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            photo.category === 'family' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400' :
                            photo.category === 'store' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400' :
                            photo.category === 'events' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                            photo.category === 'products' ? 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-400' :
                            photo.category === 'customers' ? 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-400' :
                            'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400'
                          }`}>
                            {categories.find(c => c.id === photo.category)?.label}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => openLightbox(index)}
                          className="text-gray-500 dark:text-gray-400 hover:text-amber-600 transition-colors"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleLike(photo.id)}
                          className={`transition-colors ${
                            photo.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400 hover:text-red-500'
                          }`}
                        >
                          <Heart className={`h-4 w-4 ${photo.isLiked ? 'fill-current' : ''}`} />
                        </button>
                        <button className="text-gray-500 dark:text-gray-400 hover:text-blue-500 transition-colors">
                          <Share2 className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDelete(photo.id)}
                          className="text-gray-500 dark:text-gray-400 hover:text-red-500 transition-colors"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {filteredAndSortedPhotos.length === 0 && (
          <div className="p-16 text-center">
            <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
              <ImageIcon className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
              {photos.length === 0 && !loading ? 'Gallery Setup Required' : 'Walang Photos na Nakita'}
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
              {photos.length === 0 && !loading
                ? 'The gallery database table needs to be created. Please run the create-gallery-table.sql script in your Supabase dashboard.'
                : 'Try adjusting your search terms or filter criteria to find the photos you\'re looking for.'
              }
            </p>
            {/* Debug Info */}
            <div className="text-xs text-gray-500 mb-4">
              Debug: Total photos: {photos?.length || 0}, Filtered: {filteredAndSortedPhotos.length}, Loading: {loading ? 'Yes' : 'No'}
            </div>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedCategory('all')
                }}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors bg-white dark:bg-slate-700 text-gray-900 dark:text-white text-sm"
              >
                Clear Filters
              </button>
              <button
                onClick={() => setIsUploadModalOpen(true)}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm transition-all shadow-md flex items-center"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add First Photo
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Upload Modal */}
      {isUploadModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-3 bg-gradient-to-r from-amber-600 to-yellow-700 rounded-xl shadow-lg">
                    <Upload className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      Add New Photo
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Upload a new photo to your family gallery
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setIsUploadModalOpen(false)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <form onSubmit={(e) => { e.preventDefault(); handleUpload(); }} className="p-6 space-y-6">
              {/* Drag and Drop File Upload */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Photo File
                </label>
                <div
                  onDragOver={handleDragOver}
                  onDrop={handleDrop}
                  onClick={() => fileInputRef.current?.click()}
                  className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center cursor-pointer hover:border-amber-400 dark:hover:border-amber-500 transition-colors"
                >
                  {uploadForm.file ? (
                    <div className="space-y-2">
                      <ImageIcon className="h-12 w-12 text-amber-600 mx-auto" />
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {uploadForm.file.name}
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        {(uploadForm.file.size / (1024 * 1024)).toFixed(1)} MB
                      </p>
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation()
                          setUploadForm({ ...uploadForm, file: null })
                        }}
                        className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                      >
                        Remove file
                      </button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <Upload className="h-12 w-12 text-gray-400 mx-auto" />
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        Click to upload or drag and drop
                      </p>
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        PNG, JPG, GIF up to 10MB
                      </p>
                    </div>
                  )}
                </div>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="hidden"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    value={uploadForm.title}
                    onChange={(e) => setUploadForm({ ...uploadForm, title: e.target.value })}
                    placeholder="Enter photo title..."
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"
                    required
                  />
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Category
                  </label>
                  <select
                    value={uploadForm.category}
                    onChange={(e) => setUploadForm({ ...uploadForm, category: e.target.value as Photo['category'] })}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"
                  >
                    {categories.slice(1).map(category => (
                      <option key={category.id} value={category.id}>{category.label}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <textarea
                  value={uploadForm.description}
                  onChange={(e) => setUploadForm({ ...uploadForm, description: e.target.value })}
                  rows={4}
                  placeholder="Describe your photo..."
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags (comma separated)
                  </label>
                  <input
                    type="text"
                    value={uploadForm.tags}
                    onChange={(e) => setUploadForm({ ...uploadForm, tags: e.target.value })}
                    placeholder="family, celebration, milestone..."
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"
                  />
                </div>

                {/* Location */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Location (optional)
                  </label>
                  <input
                    type="text"
                    value={uploadForm.location}
                    onChange={(e) => setUploadForm({ ...uploadForm, location: e.target.value })}
                    placeholder="Cebu City, Philippines..."
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent dark:bg-slate-700"
                  />
                </div>
              </div>

              {/* Privacy Setting */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_private"
                  checked={uploadForm.is_private}
                  onChange={(e) => setUploadForm({ ...uploadForm, is_private: e.target.checked })}
                  className="w-4 h-4 text-amber-600 bg-white border-gray-300 rounded focus:ring-amber-500 focus:ring-2"
                />
                <label htmlFor="is_private" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Make this photo private (only visible to family members)
                </label>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <button
                  type="button"
                  onClick={() => setIsUploadModalOpen(false)}
                  className="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-slate-700 font-medium transition-colors"
                  disabled={uploading}
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleUpload}
                  className="flex-1 bg-gradient-to-r from-amber-600 to-yellow-700 hover:from-amber-700 hover:to-yellow-800 text-white px-6 py-3 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 shadow-lg"
                  disabled={uploading}
                >
                  {uploading ? (
                    <>
                      <RefreshCw className="h-4 w-4 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4" />
                      Upload Photo
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Enhanced Lightbox Modal */}
      {isLightboxOpen && filteredAndSortedPhotos[currentPhotoIndex] && (
        <div className="fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50 animate-fade-in">
          <div className="relative w-full h-full flex items-center justify-center p-4">
            {/* Close Button */}
            <button
              onClick={closeLightbox}
              className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Navigation Buttons */}
            {filteredAndSortedPhotos.length > 1 && (
              <>
                <button
                  onClick={prevPhoto}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
                >
                  <ChevronLeft className="h-8 w-8" />
                </button>
                <button
                  onClick={nextPhoto}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-3 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
                >
                  <ChevronRight className="h-8 w-8" />
                </button>
              </>
            )}

            {/* Slideshow Controls */}
            <div className="absolute top-4 left-4 z-10 flex items-center gap-2">
              <button
                onClick={toggleSlideshow}
                className="p-2 bg-black bg-opacity-50 text-white rounded-full hover:bg-opacity-70 transition-all"
              >
                {isSlideshow ? <Pause className="h-5 w-5" /> : <Play className="h-5 w-5" />}
              </button>
              <span className="text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded-full">
                {currentPhotoIndex + 1} / {filteredAndSortedPhotos.length}
              </span>
            </div>

            {/* Main Image */}
            <div className="max-w-4xl max-h-full flex items-center justify-center">
              <div className="relative bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg flex items-center justify-center" style={{ minWidth: '600px', minHeight: '400px' }}>
                <ImageIcon className="h-32 w-32 text-gray-400" />

                {/* Photo Info Overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white">
                  <h3 className="text-xl font-semibold mb-2">
                    {filteredAndSortedPhotos[currentPhotoIndex].title}
                  </h3>
                  <p className="text-sm opacity-90 mb-3">
                    {filteredAndSortedPhotos[currentPhotoIndex].description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {new Date(filteredAndSortedPhotos[currentPhotoIndex].date).toLocaleDateString('en-PH')}
                      </span>
                      <span className="flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        {filteredAndSortedPhotos[currentPhotoIndex]?.views || 0}
                      </span>
                      <span className="flex items-center gap-1">
                        <Heart className="h-4 w-4" />
                        {filteredAndSortedPhotos[currentPhotoIndex]?.likes || 0}
                      </span>
                    </div>

                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => {
                          const currentPhoto = filteredAndSortedPhotos[currentPhotoIndex]
                          if (currentPhoto) {
                            handleLike(currentPhoto.id)
                          }
                        }}
                        className={`p-2 rounded-full transition-all ${
                          filteredAndSortedPhotos[currentPhotoIndex]?.isLiked
                            ? 'bg-red-500 text-white'
                            : 'bg-white bg-opacity-20 text-white hover:bg-opacity-30'
                        }`}
                      >
                        <Heart className={`h-4 w-4 ${filteredAndSortedPhotos[currentPhotoIndex]?.isLiked ? 'fill-current' : ''}`} />
                      </button>
                      <button className="p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all">
                        <Share2 className="h-4 w-4" />
                      </button>
                      <button className="p-2 bg-white bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all">
                        <Download className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Photo Detail Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fade-in">
          <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className={`p-3 rounded-xl shadow-lg ${
                    selectedPhoto.category === 'family' ? 'bg-blue-500' :
                    selectedPhoto.category === 'store' ? 'bg-green-500' :
                    selectedPhoto.category === 'events' ? 'bg-yellow-500' :
                    selectedPhoto.category === 'products' ? 'bg-amber-600' :
                    selectedPhoto.category === 'customers' ? 'bg-pink-500' :
                    'bg-orange-500'
                  }`}>
                    <ImageIcon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                      Photo Details
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {selectedPhoto.title}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedPhoto(null)}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="h-5 w-5 text-gray-500" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Photo Preview */}
                <div>
                  <div className="aspect-video bg-gradient-to-br from-amber-100 to-yellow-200 dark:from-amber-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center">
                    <ImageIcon className="h-24 w-24 text-gray-400" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => handleLike(selectedPhoto.id)}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-all ${
                          selectedPhoto.isLiked
                            ? 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400'
                            : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 hover:bg-red-50 hover:text-red-600'
                        }`}
                      >
                        <Heart className={`h-4 w-4 ${selectedPhoto.isLiked ? 'fill-current' : ''}`} />
                        {selectedPhoto.likes}
                      </button>

                      <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-blue-50 hover:text-blue-600 transition-all">
                        <Share2 className="h-4 w-4" />
                        Share
                      </button>

                      <button className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400 rounded-lg hover:bg-green-50 hover:text-green-600 transition-all">
                        <Download className="h-4 w-4" />
                        Download
                      </button>
                    </div>
                  </div>
                </div>

                {/* Photo Information */}
                <div className="space-y-6">
                  {/* Basic Info */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Information
                    </h4>

                    <div className="space-y-3">
                      <div>
                        <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                          Title
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white mt-1">
                          {selectedPhoto.title}
                        </p>
                      </div>

                      <div>
                        <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                          Description
                        </label>
                        <p className="text-sm text-gray-900 dark:text-white mt-1">
                          {selectedPhoto.description || 'No description provided'}
                        </p>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                            Category
                          </label>
                          <p className="text-sm text-gray-900 dark:text-white mt-1">
                            {categories.find(c => c.id === selectedPhoto.category)?.label}
                          </p>
                        </div>

                        <div>
                          <label className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                            Date
                          </label>
                          <p className="text-sm text-gray-900 dark:text-white mt-1">
                            {new Date(selectedPhoto.date).toLocaleDateString('en-PH')}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Statistics */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Statistics
                    </h4>

                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <Eye className="h-5 w-5 text-blue-500 mx-auto mb-1" />
                        <p className="text-lg font-bold text-gray-900 dark:text-white">{selectedPhoto.views}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Views</p>
                      </div>

                      <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <Heart className="h-5 w-5 text-red-500 mx-auto mb-1" />
                        <p className="text-lg font-bold text-gray-900 dark:text-white">{selectedPhoto.likes}</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Likes</p>
                      </div>

                      <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <Share2 className="h-5 w-5 text-green-500 mx-auto mb-1" />
                        <p className="text-lg font-bold text-gray-900 dark:text-white">0</p>
                        <p className="text-xs text-gray-500 dark:text-gray-400">Shares</p>
                      </div>
                    </div>
                  </div>

                  {/* Tags */}
                  {selectedPhoto.tags.length > 0 && (
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                        Tags
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedPhoto.tags.map(tag => (
                          <span
                            key={tag}
                            className="inline-flex px-3 py-1 bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-400 text-sm rounded-full"
                          >
                            #{tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Technical Details */}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      Technical Details
                    </h4>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-500 dark:text-gray-400">File Size:</span>
                        <span className="text-gray-900 dark:text-white">{selectedPhoto.fileSize}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500 dark:text-gray-400">Dimensions:</span>
                        <span className="text-gray-900 dark:text-white">{selectedPhoto.dimensions}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-500 dark:text-gray-400">Uploaded by:</span>
                        <span className="text-gray-900 dark:text-white">{selectedPhoto.uploadedBy}</span>
                      </div>
                      {selectedPhoto.location && (
                        <div className="flex justify-between">
                          <span className="text-gray-500 dark:text-gray-400">Location:</span>
                          <span className="text-gray-900 dark:text-white">{selectedPhoto.location}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
