#!/usr/bin/env node

/**
 * Fix Gallery Add Button Script
 * Creates the family_gallery table and fixes the Add button issue
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration')
  console.error('Please check your .env.local file for:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function fixGalleryAddButton() {
  console.log('🔧 Fixing Gallery Add Button Issue...')
  console.log('📊 Checking family_gallery table status...')

  try {
    // Test if table exists by trying to query it
    const { data, error } = await supabase
      .from('family_gallery')
      .select('id')
      .limit(1)

    if (error && error.code === '42P01') {
      console.log('❌ family_gallery table does not exist!')
      console.log('📝 This is why the Add Photo button is not working.')
      console.log('')
      console.log('🛠️  SOLUTION: Create the table manually in Supabase')
      console.log('📋 Steps to fix:')
      console.log('1. Go to: https://supabase.com/dashboard/project/[your-project]/sql')
      console.log('2. Copy the SQL from create-gallery-table.sql file')
      console.log('3. Paste and run it in the SQL Editor')
      console.log('')
      console.log('📄 SQL to run:')
      console.log('=====================================')
      
      const fs = require('fs')
      const path = require('path')
      
      try {
        const sqlContent = fs.readFileSync(path.join(__dirname, '..', 'create-gallery-table.sql'), 'utf8')
        console.log(sqlContent)
      } catch (readError) {
        console.log('-- Create family_gallery table')
        console.log('CREATE TABLE IF NOT EXISTS family_gallery (')
        console.log('    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,')
        console.log('    title VARCHAR(255) NOT NULL,')
        console.log('    description TEXT,')
        console.log('    image_url TEXT NOT NULL,')
        console.log('    image_public_id TEXT NOT NULL,')
        console.log('    category VARCHAR(50) NOT NULL DEFAULT \'family\',')
        console.log('    tags TEXT[],')
        console.log('    location VARCHAR(255),')
        console.log('    uploaded_by VARCHAR(255) NOT NULL DEFAULT \'Admin\',')
        console.log('    file_size VARCHAR(20),')
        console.log('    dimensions VARCHAR(20),')
        console.log('    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),')
        console.log('    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),')
        console.log('    is_private BOOLEAN DEFAULT FALSE,')
        console.log('    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),')
        console.log('    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()')
        console.log(');')
        console.log('')
        console.log('-- Create indexes')
        console.log('CREATE INDEX IF NOT EXISTS idx_family_gallery_category ON family_gallery(category);')
        console.log('CREATE INDEX IF NOT EXISTS idx_family_gallery_created_at ON family_gallery(created_at DESC);')
        console.log('')
        console.log('-- Enable RLS')
        console.log('ALTER TABLE family_gallery ENABLE ROW LEVEL SECURITY;')
        console.log('')
        console.log('-- Create RLS policy')
        console.log('CREATE POLICY "Enable all operations for application" ON family_gallery FOR ALL USING (true);')
      }
      
      console.log('=====================================')
      console.log('')
      console.log('⚡ After creating the table, the Add Photo button will work!')
      return false
      
    } else if (error) {
      console.log('❌ Error checking table:', error.message)
      return false
    } else {
      console.log('✅ family_gallery table exists!')
      
      // Check if it has data
      const { data: photos, error: countError } = await supabase
        .from('family_gallery')
        .select('id')

      if (!countError) {
        console.log(`📊 Table has ${photos?.length || 0} photos`)
        console.log('✅ Add Photo button should be working!')
        
        if (photos?.length === 0) {
          console.log('💡 The table is empty. You can now add your first photo!')
        }
        
        return true
      }
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error.message)
    return false
  }
}

// Additional check for mobile navigation issue
function checkMobileNavigation() {
  console.log('')
  console.log('📱 ADDITIONAL ISSUE FOUND: Mobile Navigation')
  console.log('❌ Gallery button is missing from mobile navigation')
  console.log('📝 The Gallery nav only shows on desktop/tablet screens')
  console.log('🛠️  This needs to be fixed in AdminHeader.tsx')
  console.log('')
}

// Run the fix
fixGalleryAddButton()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Gallery Add Button is working!')
      console.log('✅ family_gallery table exists and is ready to use.')
    } else {
      console.log('\n🔧 Manual action required to fix the Add Photo button.')
    }
    
    // Always show mobile navigation issue
    checkMobileNavigation()
    
    console.log('📋 Summary of issues:')
    console.log('1. ❌ family_gallery table missing (main cause of Add button not working)')
    console.log('2. ❌ Gallery button missing from mobile navigation')
    console.log('3. ✅ Desktop Gallery navigation works correctly')
  })
  .catch((error) => {
    console.error('❌ Fix failed:', error)
  })
