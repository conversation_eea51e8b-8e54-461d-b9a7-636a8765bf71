import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function POST(request: NextRequest) {
  try {
    // Create family_gallery table using direct SQL execution
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS family_gallery (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        image_url TEXT NOT NULL,
        uploaded_by VA<PERSON>HA<PERSON>(255),
        upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        tags TEXT[],
        is_featured BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      );

      -- Create indexes for better performance (only if they don't exist)
      CREATE INDEX IF NOT EXISTS idx_family_gallery_upload_date ON family_gallery(upload_date DESC);
      CREATE INDEX IF NOT EXISTS idx_family_gallery_is_featured ON family_gallery(is_featured);
      CREATE INDEX IF NOT EXISTS idx_family_gallery_tags ON family_gallery USING GIN(tags);

      -- Enable RLS
      ALTER TABLE family_gallery ENABLE ROW LEVEL SECURITY;

      -- Drop existing policies if they exist
      DROP POLICY IF EXISTS "Allow public read access" ON family_gallery;
      DROP POLICY IF EXISTS "Allow authenticated users to insert" ON family_gallery;
      DROP POLICY IF EXISTS "Allow authenticated users to update" ON family_gallery;
      DROP POLICY IF EXISTS "Allow authenticated users to delete" ON family_gallery;

      -- Create policies
      CREATE POLICY "Allow public read access" ON family_gallery
        FOR SELECT USING (true);

      CREATE POLICY "Allow authenticated users to insert" ON family_gallery
        FOR INSERT WITH CHECK (auth.role() = 'authenticated');

      CREATE POLICY "Allow authenticated users to update" ON family_gallery
        FOR UPDATE USING (auth.role() = 'authenticated');

      CREATE POLICY "Allow authenticated users to delete" ON family_gallery
        FOR DELETE USING (auth.role() = 'authenticated');
    `

    // Execute each statement separately to avoid issues
    const statements = createTableSQL.split(';').filter(stmt => stmt.trim())
    
    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', {
          sql: statement.trim() + ';'
        })
        
        if (error) {
          console.error('SQL execution error:', error)
          // Continue with other statements even if one fails
        }
      }
    }

    return NextResponse.json({ message: 'Migration completed successfully' })
  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json({ error: 'Migration failed' }, { status: 500 })
  }
}
