#!/usr/bin/env node

/**
 * Run Gallery Migration Script
 * Executes the family_gallery table migration
 */

const fs = require('fs')
const path = require('path')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase configuration')
  console.error('Please check your .env.local file for:')
  console.error('- NEXT_PUBLIC_SUPABASE_URL')
  console.error('- SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

async function runGalleryMigration() {
  console.log('🚀 Running Family Gallery Migration...')
  console.log('📁 Reading migration file: database/migrations/add_family_gallery_table.sql')

  try {
    // Read the migration file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'add_family_gallery_table.sql')
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration file not found:', migrationPath)
      return false
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    console.log('✅ Migration file loaded successfully')
    console.log(`📊 SQL content length: ${migrationSQL.length} characters`)

    console.log('')
    console.log('📝 MANUAL MIGRATION REQUIRED:')
    console.log('=====================================')
    console.log('Since automatic SQL execution is not available, please:')
    console.log('')
    console.log('1. 🌐 Go to your Supabase Dashboard:')
    console.log(`   ${supabaseUrl.replace('/rest/v1', '')}/project/[your-project-id]/sql`)
    console.log('')
    console.log('2. 📋 Copy the ENTIRE content from this file:')
    console.log('   database/migrations/add_family_gallery_table.sql')
    console.log('')
    console.log('3. 📝 Paste it into the SQL Editor')
    console.log('')
    console.log('4. ▶️  Click "Run" to execute the migration')
    console.log('')
    console.log('=====================================')
    console.log('')
    console.log('🎯 This migration will:')
    console.log('✅ Create the family_gallery table')
    console.log('✅ Add proper indexes for performance')
    console.log('✅ Enable Row Level Security (RLS)')
    console.log('✅ Add sample photos for testing')
    console.log('✅ Create triggers for automatic timestamps')
    console.log('')
    console.log('📋 Migration Preview (first 500 characters):')
    console.log('-------------------------------------')
    console.log(migrationSQL.substring(0, 500) + '...')
    console.log('-------------------------------------')
    console.log('')

    return false // Manual execution required

  } catch (error) {
    console.error('❌ Error reading migration file:', error.message)
    return false
  }
}

// Run the migration
runGalleryMigration()
  .then((success) => {
    if (success) {
      console.log('\n🎉 Migration completed successfully!')
    } else {
      console.log('\n📋 NEXT STEPS AFTER RUNNING THE MIGRATION:')
      console.log('1. ✅ Mobile Gallery navigation - ALREADY FIXED')
      console.log('2. ✅ Add Photo button logic - ALREADY FIXED')
      console.log('3. ❌ Database table - RUN THE MIGRATION ABOVE')
      console.log('')
      console.log('⚡ After running the migration:')
      console.log('   • Refresh the Gallery page')
      console.log('   • Click "Add Photo" button')
      console.log('   • Upload your first photo!')
      console.log('')
      console.log('🎯 The migration includes sample photos, so you should see some photos immediately!')
    }
  })
  .catch((error) => {
    console.error('❌ Migration failed:', error)
  })
